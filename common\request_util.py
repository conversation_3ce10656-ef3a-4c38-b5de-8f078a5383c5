
import requests

class RequestUtil:
    """
    HTTP请求工具类，封装了requests库的常用方法，支持会话管理。
    """
    def __init__(self):
        """
        初始化RequestUtil，创建一个requests会话，用于保持会话状态（如cookies）。
        """
        self.session = requests.Session()

    def send_request(self, method, url, **kwargs):
        """
        发送HTTP请求。

        Args:
            method (str): HTTP请求方法，如 'GET', 'POST', 'PUT', 'DELETE'。
            url (str): 请求的URL。
            **kwargs: 其他传递给requests请求方法的参数，如params, data, json, headers等。

        Returns:
            requests.Response: HTTP响应对象。

        Raises:
            ValueError: 如果传入了不支持的HTTP方法。
        """
        method = method.upper()  # 将请求方法转换为大写，统一处理
        try:
            if method == 'GET':
                response = self.session.get(url, **kwargs)
            elif method == 'POST':
                response = self.session.post(url, **kwargs)
            elif method == 'PUT':
                response = self.session.put(url, **kwargs)
            elif method == 'DELETE':
                response = self.session.delete(url, **kwargs)
            else:
                # 如果方法不支持，抛出ValueError
                raise ValueError(f"不支持的HTTP方法: {method}")
            return response
        except requests.exceptions.RequestException as e:
            # 捕获requests库可能抛出的所有请求异常，并重新抛出更友好的中文异常
            raise ConnectionError(f"请求发送失败，请检查网络连接或URL是否正确: {e}")



    def close_session(self):
        """
        关闭requests会话，释放资源。
        """
        self.session.close()


