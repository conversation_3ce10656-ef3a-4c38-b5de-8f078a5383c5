import pytest
from common.assert_util import AssertUtil
from common.extract_util import ExtractUtil

class TestUserLogin:
    """
    用户登录接口的Pytest测试用例。
    通过fixture获取request_util和config。
    """
    def test_login_success(self, request_util, config):
        """
        测试用例：用户成功登录。
        """
        # 1. 从config fixture中获取配置信息
        base_url = config["DEFAULT"]["BASE_URL"]
        login_name = config["USER"]["LOGIN_NAME"]
        password = config["USER"]["PASSWORD"]
        uuid = config["USER"]["UUID"]

        # 2. 准备测试数据
        login_data = {
            "code": "9999",
            "loginName": login_name,
            "password": password,
            "uuid": uuid
        }

        # 3. 发送请求
        response = request_util.send_request(
            method="POST",
            url=f"{base_url}/jshERP-boot/user/login",
            json=login_data
        )

        # 4. 断言结果
        AssertUtil.assert_status_code(response, 200)
        AssertUtil.assert_json_value(response, "code", 200)
        AssertUtil.assert_in_text(response, "token")

        # 5. 提取参数（可选）
        # 从成功的登录响应中提取token，可用于后续需要认证的接口测试
        token = ExtractUtil.extract_json_value(response, "data.token")
        print(f"成功提取到token: {token}")

    def test_login_fail_wrong_password(self, request_util, config):
        """
        测试用例：使用错误的密码登录失败。
        """
        # 1. 从config fixture中获取配置信息
        base_url = config["DEFAULT"]["BASE_URL"]
        login_name = config["USER"]["LOGIN_NAME"]
        uuid = config["USER"]["UUID"]

        # 2. 准备测试数据
        login_data = {
            "code": "9999",
            "loginName": login_name,
            "password": "wrong_password", # 使用一个明显错误的密码
            "uuid": uuid
        }

        # 3. 发送请求
        response = request_util.send_request(
            method="POST",
            url=f"{base_url}/jshERP-boot/user/login",
            json=login_data
        )

        # 4. 断言结果
        AssertUtil.assert_status_code(response, 200)
        # 假设失败时，业务状态码不为0，这里以400为例，请根据实际接口返回调整
        AssertUtil.assert_json_value(response, "code", 200)
        AssertUtil.assert_json_value(response, "data.msgTip", "user password error")


