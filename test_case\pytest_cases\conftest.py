import pytest
import configparser
from common.request_util import RequestUtil

@pytest.fixture(scope="session")
def request_util():
    """
    Pytest fixture：在整个测试会话期间只执行一次，提供一个RequestUtil实例。
    用于管理HTTP请求会话，并在会话结束后关闭。
    """
    util = RequestUtil()
    yield util  # 将RequestUtil实例提供给测试函数
    util.close_session() # 测试会话结束后关闭session

@pytest.fixture(scope="class")
def config():
    """
    Pytest fixture：在每个测试类实例化前执行一次，提供一个ConfigParser实例。
    用于加载配置文件，供测试类中的方法使用。
    """
    conf = configparser.ConfigParser()
    # 注意：这里的路径需要根据实际项目结构调整
    conf.read(r"D:\PythonDemo\base_api_test_framework\config\config.ini", encoding='utf-8')
    return conf


