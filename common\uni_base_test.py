import unittest
import configparser
from common.request_util import RequestUtil

class UniBaseTest(unittest.TestCase):
    """
    所有unittest接口测试用例的基类。
    提供了HTTP请求会话的初始化和关闭，以及配置文件的加载。
    """
    @classmethod
    def setUpClass(cls):
        """
        在所有测试方法运行前执行一次，用于初始化共享资源。
        这里初始化RequestUtil会话和加载配置文件。
        """
        cls.request_util = RequestUtil()
        cls.config = configparser.ConfigParser()
        # 注意：这里的路径需要根据实际项目结构调整
        cls.config.read(r'D:\PythonDemo\base_api_test_framework\config\config.ini', encoding='utf-8')
        cls.base_url = cls.config["DEFAULT"]["BASE_URL"]    # http://azuretest2025.natapp1.cc/

    @classmethod
    def tearDownClass(cls):
        """
        在所有测试方法运行后执行一次，用于清理共享资源。
        这里关闭RequestUtil会话。
        """
        cls.request_util.close_session()

    def setUp(self):
        """
        在每个测试方法运行前执行，可以用于准备每个测试用例独立的数据。
        """
        pass

    def tearDown(self):
        """
        在每个测试方法运行后执行，可以用于清理每个测试用例独立的数据。
        """
        pass


