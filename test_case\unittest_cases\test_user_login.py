import unittest
from common.uni_base_test import UniBaseTest
from common.assert_util import AssertUtil
from common.extract_util import ExtractUtil


class TestUserLogin(UniBaseTest):
    """
    用户登录接口的测试用例。
    继承自BaseTest，自动拥有HTTP请求能力和配置读取能力。
    """
    def setUp(self):
        """
        从配置文件中读取当前测试用例所需的特定数据。
        """
        self.login_name = self.config["USER"]["LOGIN_NAME"]
        self.password = self.config["USER"]["PASSWORD"]
        self.uuid = self.config["USER"]["UUID"]
        print("login_name", self.login_name)
        print("password" ,self.password)
        print("uuid", self.uuid)

    def test_login_success(self):
        """
        测试用例：用户成功登录。
        """
        # 1. 准备测试数据
        login_data = {
            "code": "9999",
            "loginName": self.login_name,
            "password": self.password,
            "uuid": self.uuid
        }

        # 2. 发送请求
        response = self.request_util.send_request(
            method="POST",
            url=f"{self.base_url}/jshERP-boot/user/login",      # http://azuretest2025.natapp1.cc/jshERP-boot/user/login
            json=login_data
        )
        print("response", response)
        # 3. 断言结果
        AssertUtil.assert_status_code(response, 200)
        AssertUtil.assert_json_value(response, "code", 200)
        AssertUtil.assert_json_value(response, "data.user.id", 356)
        AssertUtil.assert_in_text(response, "token")

        # 4. 提取参数（可选）
        # 从成功的登录响应中提取token，可用于后续需要认证的接口测试
        token = ExtractUtil.extract_json_value(response, "data.token")
        print(f"成功提取到token: {token}")
        # 可以将token保存到类属性或全局变量中，供其他测试用例使用
        setattr(self, "token", token) # 例如，将其设置为类的一个属性

    def test_login_fail_wrong_password(self):
        """
        测试用例：使用错误的密码登录失败。
        """
        # 1. 准备测试数据
        login_data = {
            "code": "9999",
            "loginName": self.login_name,
            "password": "wrong_password", # 使用一个明显错误的密码
            "uuid": self.uuid
        }

        # 2. 发送请求
        response = self.request_util.send_request(
            method="POST",
            url=f"{self.base_url}/jshERP-boot/user/login",
            json=login_data
        )

        # 3. 断言结果
        AssertUtil.assert_status_code(response, 200)
        # 假设失败时，业务状态码不为0，这里以400为例，请根据实际接口返回调整
        AssertUtil.assert_json_value(response, "code", 200)
        AssertUtil.assert_json_value(response, "data.msgTip", "user password error")

    def test_login_fail_wrong_username(self):
        """
        测试用例：使用错误的账户名登录失败。
        """
        # 1. 准备测试数据
        login_data = {
            "code": "9999",
            "loginName": "lisi",
            "password": self.password, # 使用一个明显错误的密码
            "uuid": self.uuid
        }

        # 2. 发送请求
        response = self.request_util.send_request(
            method="POST",
            url=f"{self.base_url}/jshERP-boot/user/login",
            json=login_data
        )

        # 3. 断言结果
        AssertUtil.assert_status_code(response, 200)
        # 假设失败时，业务状态码不为0，这里以400为例，请根据实际接口返回调整
        AssertUtil.assert_json_value(response, "code", 200)
        AssertUtil.assert_json_value(response, "data.msgTip", "user is not exist")


