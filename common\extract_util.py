import json

class ExtractUtil:
    """
    参数提取工具类，用于从HTTP响应中提取指定数据。
    """
    @staticmethod
    def extract_json_value(response, json_path):
        """
        从JSON响应中提取指定路径的值。

        Args:
            response (requests.Response): HTTP响应对象。
            json_path (str): JSON路径，使用点（.）分隔，例如 "data.token"。

        Returns:
            any: 提取到的值。

        Raises:
            ValueError: 如果响应内容不是有效的JSON格式。
            KeyError: 如果指定的JSON路径在响应中不存在。
        """
        try:
            response_json = response.json()
        except json.JSONDecodeError:
            raise ValueError("提取失败：响应内容不是有效的JSON格式")

        current_value = response_json
        path_elements = json_path.split(".")
        for element in path_elements:
            if isinstance(current_value, dict) and element in current_value:
                current_value = current_value[element]
            elif isinstance(current_value, list) and element.isdigit() and int(element) < len(current_value):
                current_value = current_value[int(element)]
            else:
                raise KeyError(f"提取失败：在JSON响应中未找到路径 '{json_path}' (在元素 '{element}' 处中断)")
        return current_value


