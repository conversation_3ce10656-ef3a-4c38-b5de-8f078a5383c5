import json

class AssertUtil:
    """
    断言工具类，封装了常用的断言方法，使测试用例更清晰、易读。
    """
    @staticmethod
    def assert_status_code(response, expected_code):
        """
        断言HTTP响应的状态码。

        Args:
            response (requests.Response): HTTP响应对象。
            expected_code (int): 期望的HTTP状态码。
        """
        assert response.status_code == expected_code, \
            f"断言失败：期望状态码为 {expected_code}，但实际为 {response.status_code}"

    @staticmethod
    def assert_json_value(response, json_path, expected_value):
        """
        断言JSON响应中指定路径的值。

        Args:
            response (requests.Response): HTTP响应对象。
            json_path (str): JSON路径，使用点（.）分隔，例如 "data.user.id"。
            expected_value: 期望在该路径下找到的值。
        """
        try:
            response_json = response.json()
        except json.JSONDecodeError:
            raise ValueError("断言失败：响应内容不是有效的JSON格式")

        # 通过json_path解析JSON
        current_value = response_json
        path_elements = json_path.split(".")
        for element in path_elements:
            if isinstance(current_value, dict) and element in current_value:
                current_value = current_value[element]
            elif isinstance(current_value, list) and element.isdigit() and int(element) < len(current_value):
                current_value = current_value[int(element)]
            else:
                raise KeyError(f"断言失败：在JSON响应中未找到路径 '{json_path}' (在元素 '{element}' 处中断)")
        assert current_value == expected_value, f"断言失败：在路径 '{json_path}' 处，期望值为 '{expected_value}'，但实际值为 '{current_value}'"

    @staticmethod
    def assert_in_text(response, expected_text):
        """
        断言响应的文本内容中包含期望的字符串。

        Args:
            response (requests.Response): HTTP响应对象。
            expected_text (str): 期望包含的文本。
        """
        assert expected_text in response.text, \
            f"断言失败：期望在响应文本中找到 '{expected_text}'，但未找到"

    @staticmethod
    def assert_not_in_text(response, unexpected_text):
        """
        断言响应的文本内容中不包含指定的字符串。

        Args:
            response (requests.Response): HTTP响应对象。
            unexpected_text (str): 不期望包含的文本。
        """
        assert unexpected_text not in response.text, \
            f"断言失败：不期望在响应文本中找到 '{unexpected_text}'，但找到了"

